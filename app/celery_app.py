"""
Celery application configuration for background tasks
"""
from celery import Celery
from config import Config
import os


def make_celery(app):
    """Create and configure Celery app"""
    celery = Celery(
        app.import_name,
        backend=app.config['CELERY_RESULT_BACKEND'],
        broker=app.config['CELERY_BROKER_URL'],
        include=['app.tasks.stats_tasks', 'app.tasks.match_tasks', 'app.tasks.maintenance_tasks']
    )
    
    # Update configuration
    celery.conf.update(
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,
        task_track_started=True,
        task_time_limit=30 * 60,  # 30 minutes
        task_soft_time_limit=25 * 60,  # 25 minutes
        worker_prefetch_multiplier=1,
        worker_max_tasks_per_child=1000,
        # Periodic task configuration
        beat_schedule={
            'update-all-profiles-stats': {
                'task': 'app.tasks.stats_tasks.update_all_profiles_stats',
                'schedule': 3600.0,  # Every hour
            },
            'fetch-recent-matches': {
                'task': 'app.tasks.match_tasks.fetch_recent_matches_for_all_profiles',
                'schedule': 1800.0,  # Every 30 minutes
            },
            'cleanup-old-tasks': {
                'task': 'app.tasks.maintenance_tasks.cleanup_old_task_results',
                'schedule': 86400.0,  # Daily
            },
            'check-achievements': {
                'task': 'app.tasks.maintenance_tasks.check_all_user_achievements',
                'schedule': 21600.0,  # Every 6 hours
            },
        },
    )
    
    class ContextTask(celery.Task):
        """Make celery tasks work with Flask app context"""
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)
    
    celery.Task = ContextTask
    return celery


# Create Flask app and Celery instance
def create_celery_app():
    """Create celery app with Flask context"""
    # Import here to avoid circular imports
    from app import create_app
    flask_app = create_app()
    return make_celery(flask_app)

# Initialize celery lazily to avoid circular imports
celery = None

def get_celery():
    """Get or create celery instance"""
    global celery
    if celery is None:
        celery = create_celery_app()
    return celery


if __name__ == '__main__':
    get_celery().start()
