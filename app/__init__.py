"""
Game Stats Tracker Application Package
"""
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>gin<PERSON><PERSON><PERSON>
from flask_migrate import Migrate
from flask_mail import Mail
import redis
from config import Config

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()
migrate = Migrate()
mail = Mail()
redis_client = None

# Export commonly used objects
__all__ = ['create_app', 'db', 'login_manager', 'migrate', 'mail', 'redis_client']


def create_app(config_class=Config):
    """Create and configure Flask application"""
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize extensions with app
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    mail.init_app(app)

    # Initialize Redis client
    global redis_client
    try:
        redis_client = redis.from_url(app.config['REDIS_URL'])
        # Test the connection
        redis_client.ping()
    except Exception as e:
        app.logger.warning(f"Failed to initialize Redis cache: {e}")
        redis_client = None

    # Initialize cache
    from app.utils.cache import cache
    cache.init_app(app)

    # Configure login manager
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'

    @login_manager.user_loader
    def load_user(user_id):
        from app.models.user import User
        return User.query.get(int(user_id))

    # Register blueprints
    from app.routes.main import main_bp
    from app.routes.auth import auth_bp
    from app.routes.dashboard import dashboard_bp
    from app.routes.api import api_bp
    from app.routes.social import social_bp

    app.register_blueprint(main_bp)
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(social_bp)

    return app
