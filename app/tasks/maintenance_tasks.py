"""
Celery tasks for system maintenance and cleanup
"""
from app.celery_app import get_celery
from app import db
from app.models.match import Match
from app.models.player_stats import PlayerStats
from app.models.user import User
from app.services.achievement_service import AchievementService
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

# Get the celery instance
celery = get_celery()

@celery.task(bind=True)
def cleanup_old_task_results(self):
    """
    Clean up old Celery task results and temporary data
    
    Returns:
        dict: Cleanup results
    """
    try:
        # Clean up task results older than 7 days
        cutoff_date = datetime.utcnow() - timedelta(days=7)
        
        # This would clean up Celery result backend
        # Implementation depends on the result backend used
        
        logger.info("Cleaned up old task results")
        
        return {
            'success': True,
            'cleaned_up_date': cutoff_date.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up task results: {e}")
        return {'success': False, 'error': str(e)}


@celery.task(bind=True)
def cleanup_old_matches(self, days_to_keep=90):
    """
    Clean up very old match data to save space
    
    Args:
        days_to_keep (int): Number of days of match data to keep
        
    Returns:
        dict: Cleanup results
    """
    try:
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        
        # Find old matches
        old_matches = Match.query.filter(Match.match_date < cutoff_date).all()
        
        deleted_count = 0
        for match in old_matches:
            # Delete associated player stats first
            PlayerStats.query.filter_by(match_id=match.id).delete()
            db.session.delete(match)
            deleted_count += 1
        
        db.session.commit()
        
        logger.info(f"Cleaned up {deleted_count} old matches")
        
        return {
            'success': True,
            'deleted_matches': deleted_count,
            'cutoff_date': cutoff_date.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up old matches: {e}")
        db.session.rollback()
        return {'success': False, 'error': str(e)}


@celery.task(bind=True)
def health_check(self):
    """
    Perform system health checks
    
    Returns:
        dict: Health check results
    """
    try:
        # Check database connectivity
        db.session.execute('SELECT 1')
        
        # Check if we can import services
        from app.services import get_valorant_service, get_cs2_service, get_bgmi_service
        
        services_status = {
            'valorant': get_valorant_service() is not None,
            'cs2': get_cs2_service() is not None,
            'bgmi': get_bgmi_service() is not None
        }
        
        return {
            'success': True,
            'database': 'healthy',
            'services': services_status,
            'timestamp': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            'success': False,
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }


@celery.task(bind=True)
def check_all_user_achievements(self):
    """Check achievements for all users"""
    try:
        logger.info("Starting achievement check for all users")

        # Initialize achievements if needed
        AchievementService.initialize_achievements()

        # Get all active users
        users = User.query.filter_by(is_active=True).all()

        total_new_achievements = 0
        for user in users:
            try:
                newly_earned = AchievementService.check_user_achievements(user.id)
                if newly_earned:
                    total_new_achievements += len(newly_earned)
                    logger.info(f"User {user.id} earned {len(newly_earned)} new achievements")
            except Exception as e:
                logger.error(f"Error checking achievements for user {user.id}: {e}")
                continue

        logger.info(f"Achievement check completed. {total_new_achievements} new achievements awarded")
        return f"Checked achievements for {len(users)} users, awarded {total_new_achievements} new achievements"

    except Exception as e:
        logger.error(f"Error in achievement check task: {e}")
        raise self.retry(countdown=300, max_retries=3)
