"""
Celery tasks for updating player statistics
"""
from app.celery_app import get_celery
from app import db
from app.models.player_profile import PlayerProfile
from app.models.game import Game
from app.services import get_service_for_game, APIException, PlayerNotFoundException
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

# Get the celery instance
celery = get_celery()

@celery.task(bind=True, max_retries=3)
def update_profile_stats(self, profile_id):
    """
    Update statistics for a single player profile
    
    Args:
        profile_id (int): ID of the PlayerProfile to update
        
    Returns:
        dict: Update results with success status and stats
    """
    try:
        profile = PlayerProfile.query.get(profile_id)
        if not profile:
            logger.error(f"Profile {profile_id} not found")
            return {'success': False, 'error': 'Profile not found'}
        
        if not profile.is_active:
            logger.info(f"Skipping inactive profile {profile_id}")
            return {'success': True, 'skipped': True, 'reason': 'Profile inactive'}
        
        # Get API service for the game
        api_service = get_service_for_game(profile.game.api_name)
        if not api_service:
            logger.error(f"No API service available for {profile.game.name}")
            return {'success': False, 'error': f'No API service for {profile.game.name}'}
        
        # Determine player identifier based on game
        if profile.game.api_name == 'valorant':
            if not profile.player_tag:
                logger.error(f"Valorant profile {profile_id} missing player tag")
                return {'success': False, 'error': 'Missing player tag for Valorant'}
            identifier = f"{profile.player_name}#{profile.player_tag}"
        else:
            identifier = profile.external_id or profile.player_name
        
        # Fetch updated stats from API
        try:
            stats = api_service.get_player_stats(identifier, region=profile.region)
        except PlayerNotFoundException:
            logger.warning(f"Player not found for profile {profile_id}: {identifier}")
            return {'success': False, 'error': 'Player not found in API'}
        except APIException as e:
            logger.error(f"API error for profile {profile_id}: {e}")
            # Retry with exponential backoff
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        # Update profile with new stats
        old_matches = profile.total_matches or 0
        profile.total_matches = stats.get('total_matches', 0)
        profile.wins = stats.get('wins', 0)
        profile.losses = stats.get('losses', 0)
        profile.kd_ratio = stats.get('kd_ratio', 0.0)
        profile.avg_score = stats.get('avg_score', 0.0)
        profile.last_updated = datetime.utcnow()
        
        # Update external_id if we got a new one
        if stats.get('player_id') and not profile.external_id:
            profile.external_id = stats.get('player_id')
        
        db.session.commit()
        
        new_matches = profile.total_matches - old_matches
        logger.info(f"Updated profile {profile_id}: {new_matches} new matches")
        
        return {
            'success': True,
            'profile_id': profile_id,
            'new_matches': new_matches,
            'total_matches': profile.total_matches,
            'win_rate': profile.win_rate,
            'updated_at': profile.last_updated.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error updating profile {profile_id}: {e}")
        db.session.rollback()
        
        # Retry with exponential backoff for unexpected errors
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        return {'success': False, 'error': str(e)}


@celery.task(bind=True)
def update_all_profiles_stats(self):
    """
    Update statistics for all active player profiles
    
    Returns:
        dict: Summary of update results
    """
    try:
        # Get all active profiles that haven't been updated recently
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        profiles = PlayerProfile.query.filter(
            PlayerProfile.is_active == True,
            db.or_(
                PlayerProfile.last_updated == None,
                PlayerProfile.last_updated < cutoff_time
            )
        ).all()
        
        logger.info(f"Starting bulk update for {len(profiles)} profiles")
        
        results = {
            'total_profiles': len(profiles),
            'successful_updates': 0,
            'failed_updates': 0,
            'skipped_updates': 0,
            'errors': []
        }
        
        # Process profiles in batches to avoid overwhelming APIs
        batch_size = 10
        for i in range(0, len(profiles), batch_size):
            batch = profiles[i:i + batch_size]
            
            for profile in batch:
                try:
                    # Call the individual update task
                    result = update_profile_stats.apply_async(
                        args=[profile.id],
                        countdown=i // batch_size * 30  # Stagger requests
                    ).get(timeout=300)  # 5 minute timeout
                    
                    if result.get('success'):
                        if result.get('skipped'):
                            results['skipped_updates'] += 1
                        else:
                            results['successful_updates'] += 1
                    else:
                        results['failed_updates'] += 1
                        results['errors'].append({
                            'profile_id': profile.id,
                            'error': result.get('error', 'Unknown error')
                        })
                        
                except Exception as e:
                    logger.error(f"Error processing profile {profile.id}: {e}")
                    results['failed_updates'] += 1
                    results['errors'].append({
                        'profile_id': profile.id,
                        'error': str(e)
                    })
        
        logger.info(f"Bulk update completed: {results['successful_updates']} successful, "
                   f"{results['failed_updates']} failed, {results['skipped_updates']} skipped")
        
        return results
        
    except Exception as e:
        logger.error(f"Error in bulk update: {e}")
        return {
            'success': False,
            'error': str(e)
        }


@celery.task(bind=True)
def bulk_update_profiles_stats(self, profile_ids):
    """
    Update statistics for a specific list of profiles
    
    Args:
        profile_ids (list): List of profile IDs to update
        
    Returns:
        dict: Summary of update results
    """
    try:
        results = {
            'total_profiles': len(profile_ids),
            'successful_updates': 0,
            'failed_updates': 0,
            'errors': []
        }
        
        for profile_id in profile_ids:
            try:
                result = update_profile_stats.apply_async(args=[profile_id]).get(timeout=300)
                
                if result.get('success'):
                    results['successful_updates'] += 1
                else:
                    results['failed_updates'] += 1
                    results['errors'].append({
                        'profile_id': profile_id,
                        'error': result.get('error', 'Unknown error')
                    })
                    
            except Exception as e:
                logger.error(f"Error updating profile {profile_id}: {e}")
                results['failed_updates'] += 1
                results['errors'].append({
                    'profile_id': profile_id,
                    'error': str(e)
                })
        
        return results
        
    except Exception as e:
        logger.error(f"Error in bulk update: {e}")
        return {
            'success': False,
            'error': str(e)
        }
